"""
修改后的CE环境类，支持固定场景
"""

import numpy as np
from gym import spaces
from typing import Optional

# 假设这些是原有的导入（需要根据实际路径调整）
from auto_mst_rl_xuance.envs.base.mst_env import MSTEnv
from auto_mst_rl_xuance.envs.sim_py.ce.algo.swarm_alg_ce import swarm_alg_ce
from auto_mst_rl_xuance.envs.sim_py.ce.tools.agent_utils import get_topology_neighbors
from auto_mst_rl_xuance.envs.sim_py.ce.tools.init_simulation import (
    create_initial_runtime_state,
    init_sim_robots,
)
from auto_mst_rl_xuance.envs.sim_py.ce.tools.simulation import parallel_sim_robots

from fixed_scenario_manager import FixedScenarioManager


class CEEnvWithFixedScenarios(MSTEnv):
    """
    支持固定场景的CE环境类
    """

    def __init__(
        self,
        config: dict,
        max_episode_steps: int = 1200,
        enable_visualization: bool = False,
        use_fixed_scenarios: bool = True,
        num_train_scenarios: int = 30,
        num_eval_scenarios: int = 10,
        is_evaluation: bool = False,
        scenario_dir: str = "scenarios",
    ):
        """
        初始化CE环境

        参数:
            config (dict): 环境配置，包含仿真参数和奖励设置
            max_episode_steps (int): 最大步数
            enable_visualization (bool): 是否启用实时可视化
            use_fixed_scenarios (bool): 是否使用固定场景
            num_train_scenarios (int): 训练场景数量
            num_eval_scenarios (int): 评估场景数量
            is_evaluation (bool): 是否为评估模式
            scenario_dir (str): 场景文件目录
        """
        # 初始化基类
        super(CEEnvWithFixedScenarios, self).__init__(config, max_episode_steps)
        
        # 设置场景类型编码
        self.scenario_type_code = 0  # Chase-Escape场景

        # 实时可视化相关设置
        self.enable_visualization = enable_visualization
        self.visualization_fig = None
        self.visualization_axes = None

        # 固定场景相关设置
        self.use_fixed_scenarios = use_fixed_scenarios
        self.is_evaluation = is_evaluation
        
        if self.use_fixed_scenarios:
            self.scenario_manager = FixedScenarioManager(
                scenario_dir=scenario_dir,
                num_train_scenarios=num_train_scenarios,
                num_eval_scenarios=num_eval_scenarios
            )
            
            # 预加载场景
            max_id = getattr(self.config, "max_id", 50)
            self.scenario_manager.load_scenarios(max_id)
            
            print(f"固定场景模式: {'评估' if is_evaluation else '训练'}")
            print(f"场景信息: {self.scenario_manager.get_scenario_info(max_id)}")

        # 初始化仿真环境
        self._reset_simulation()

        # 确保agents列表在初始化时正确设置
        if not hasattr(self, "agents") or not self.agents:
            self.agents = [str(i) for i in self.runtime_state.robots_list]

        # 确保n_agents属性正确设置
        if not hasattr(self, "n_agents") or self.n_agents == 0:
            self.n_agents = len(self.agents)

        # 构建观察和动作空间
        for agent_id in self.agents:
            self.observation_space[agent_id] = spaces.Box(
                low=-10.0, high=10.0, shape=(self._get_obs_dim(),)
            )
            self.action_space[agent_id] = spaces.Box(
                low=self.mst_min, high=self.mst_max, shape=(1,)
            )

    def _reset_simulation(self):
        """
        重置CE仿真环境
        """
        # 获取配置参数
        max_id = getattr(self.config, "max_id", 50)

        # 初始化运行时状态
        self.runtime_state = create_initial_runtime_state(
            max_id=max_id, max_sim_steps_val=self.max_episode_steps
        )

        if self.use_fixed_scenarios:
            # 使用固定场景
            self._init_with_fixed_scenario(max_id)
        else:
            # 使用原有的随机初始化
            self._init_with_random_scenario(max_id)

    def _init_with_fixed_scenario(self, max_id: int):
        """
        使用固定场景初始化
        
        Args:
            max_id: 智能体数量
        """
        # 获取场景数据
        if self.is_evaluation:
            scenario_data = self.scenario_manager.get_eval_scenario(max_id)
        else:
            scenario_data = self.scenario_manager.get_train_scenario(max_id)
        
        # 使用场景数据初始化智能体
        self._init_robots_from_scenario(scenario_data)
        
        # 可选：打印当前使用的场景信息
        # print(f"使用场景: ID={scenario_data.scenario_id}, 种子={scenario_data.seed}")

    def _init_with_random_scenario(self, max_id: int):
        """
        使用随机场景初始化（原有方式）
        
        Args:
            max_id: 智能体数量
        """
        self.runtime_state = init_sim_robots(
            self.runtime_state,
            "unif",  # 均匀分布初始化
            np.array([0, 0]),  # 中心位置
            100,  # 分布半径
            True,  # 并行仿真
        )

    def _init_robots_from_scenario(self, scenario_data):
        """
        从场景数据初始化智能体
        
        Args:
            scenario_data: 场景数据
        """
        from auto_mst_rl_xuance.envs.sim_py.ce.tools.agent_utils import Agent
        
        # 初始化机器人列表
        self.runtime_state.robots_list = list(range(1, self.runtime_state.max_id + 1))
        
        # 创建智能体对象
        self.runtime_state.actors = {}
        
        for i, robot_id in enumerate(self.runtime_state.robots_list):
            # 从场景数据获取位置和速度
            pos = scenario_data.positions[:, i]
            vel = scenario_data.velocities[:, i]
            
            # 创建智能体
            agent = Agent(
                id=robot_id,
                pose=pos.copy(),
                vel=vel.copy(),
                is_activated=False,
                src_id=None,
                cj_threshold=self.runtime_state.config.cj_threshold,
                memory=[],
                desired_turn_angle=[],
                desired_speed=[],
                observed_neighbor_ids=[],
                observed_neighbor_cj_values=[],
                observed_neighbor_relative_positions=[]
            )
            
            self.runtime_state.actors[robot_id] = agent

        # 添加捕食者（hawk）
        hawk_id = self.runtime_state.config.hawk_id
        if hawk_id not in self.runtime_state.actors:
            # 捕食者位置可以固定或基于场景调整
            hawk_pos = np.array([0.0, 0.0])  # 可以根据需要调整
            hawk_vel = np.array([0.0, 0.0])
            
            hawk_agent = Agent(
                id=hawk_id,
                pose=hawk_pos,
                vel=hawk_vel,
                is_activated=False,
                src_id=None,
                cj_threshold=float('inf'),  # 捕食者不会被激活
                memory=[],
                desired_turn_angle=[],
                desired_speed=[],
                observed_neighbor_ids=[],
                observed_neighbor_cj_values=[],
                observed_neighbor_relative_positions=[]
            )
            
            self.runtime_state.actors[hawk_id] = hawk_agent

    def _simulation_step(self):
        """
        执行CE仿真步进
        """
        # 更新仿真步骤计数器
        self.runtime_state.sim_step = self.episode_step + 1

        # 计算期望转向角度和速度
        des_turn_angle, des_speed, self.runtime_state = swarm_alg_ce(self.runtime_state)

        # 更新智能体状态
        self.runtime_state = parallel_sim_robots(
            self.runtime_state, des_turn_angle, des_speed
        )

    def get_scenario_info(self) -> dict:
        """
        获取当前场景信息
        
        Returns:
            场景信息字典
        """
        if self.use_fixed_scenarios:
            max_id = getattr(self.config, "max_id", 50)
            return self.scenario_manager.get_scenario_info(max_id)
        else:
            return {"mode": "random", "use_fixed_scenarios": False}

    def reset_scenario_indices(self):
        """重置场景索引（用于新的训练/评估周期）"""
        if self.use_fixed_scenarios:
            self.scenario_manager.reset_indices()

    # 其他方法保持与原CEEnv相同
    def _get_agent_obs(self, agent_id):
        """获取智能体观察（与原实现相同）"""
        # 这里应该包含原有的观察获取逻辑
        # 为了简化，这里只返回一个占位符
        obs_dim = self._get_obs_dim()
        return np.zeros(obs_dim)
    
    def _get_obs_dim(self):
        """获取观察维度"""
        # 自身状态(4) + 邻居统计(3) + 邻居详情(5*k_neighbors) + 场景信息(5)
        return 4 + 3 + 5 * self.k_neighbors + 5
    
    def _compute_rewards(self):
        """计算奖励（与原实现相同）"""
        # 这里应该包含原有的奖励计算逻辑
        return {agent_id: 0.0 for agent_id in self.agents}
    
    def _get_termination(self):
        """检查终止条件（与原实现相同）"""
        return False
    
    def state(self):
        """返回全局状态（与原实现相同）"""
        return np.zeros(20)  # 占位符


# 测试代码
if __name__ == "__main__":
    # 测试配置
    config = {
        "max_id": 10,
        "mst_min": 0.0,
        "mst_max": 20.0,
        "k_neighbors": 7,
    }
    
    # 创建训练环境
    train_env = CEEnvWithFixedScenarios(
        config=config,
        use_fixed_scenarios=True,
        is_evaluation=False
    )
    
    # 创建评估环境
    eval_env = CEEnvWithFixedScenarios(
        config=config,
        use_fixed_scenarios=True,
        is_evaluation=True
    )
    
    print("训练环境场景信息:", train_env.get_scenario_info())
    print("评估环境场景信息:", eval_env.get_scenario_info())
    
    # 测试重置
    for i in range(3):
        train_obs, _ = train_env.reset()
        eval_obs, _ = eval_env.reset()
        print(f"重置 {i+1}: 训练环境智能体数={len(train_obs)}, 评估环境智能体数={len(eval_obs)}")
