"""
固定场景管理器
用于生成、保存和加载基于固定种子的CE场景
"""

import os
import pickle
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import datetime


@dataclass
class ScenarioData:
    """单个场景的数据结构"""
    scenario_id: int
    seed: int
    positions: np.ndarray  # (2, num_agents) 位置矩阵
    velocities: np.ndarray  # (2, num_agents) 速度矩阵
    num_agents: int
    generated_time: str
    metadata: Dict


class FixedScenarioManager:
    """固定场景管理器"""
    
    def __init__(self, 
                 scenario_dir: str = "scenarios",
                 num_train_scenarios: int = 30,
                 num_eval_scenarios: int = 10):
        """
        初始化场景管理器
        
        Args:
            scenario_dir: 场景文件存储目录
            num_train_scenarios: 训练场景数量
            num_eval_scenarios: 评估场景数量
        """
        self.scenario_dir = Path(scenario_dir)
        self.scenario_dir.mkdir(exist_ok=True)
        
        self.num_train_scenarios = num_train_scenarios
        self.num_eval_scenarios = num_eval_scenarios
        
        # 确保评估场景数量不超过训练场景数量
        assert num_eval_scenarios <= num_train_scenarios, \
            f"评估场景数量({num_eval_scenarios})不能超过训练场景数量({num_train_scenarios})"
        
        self.scenarios: List[ScenarioData] = []
        self.current_train_idx = 0
        self.current_eval_idx = 0
    
    def get_scenario_file_path(self, num_agents: int) -> Path:
        """获取场景文件路径"""
        return self.scenario_dir / f"ce_scenarios_{self.num_train_scenarios}_{num_agents}.pkl"
    
    def generate_scenarios(self, num_agents: int = 50, force_regenerate: bool = False) -> List[ScenarioData]:
        """
        生成固定场景
        
        Args:
            num_agents: 智能体数量
            force_regenerate: 是否强制重新生成
            
        Returns:
            生成的场景列表
        """
        scenario_file = self.get_scenario_file_path(num_agents)
        
        # 如果文件存在且不强制重新生成，直接加载
        if scenario_file.exists() and not force_regenerate:
            print(f"场景文件已存在: {scenario_file}")
            return self.load_scenarios(num_agents)
        
        print(f"开始生成 {self.num_train_scenarios} 个固定场景...")
        
        # 导入randpose_unif函数（需要修改为带种子版本）
        from test_seed_consistency import randpose_unif_with_seed
        
        scenarios = []
        for i in range(self.num_train_scenarios):
            seed = i + 1  # 种子从1开始
            print(f"生成场景 {i+1}/{self.num_train_scenarios} (种子: {seed})")
            
            # 使用固定种子生成位置
            positions, velocities = randpose_unif_with_seed(num_agents, seed=seed)
            
            # 创建场景数据
            scenario = ScenarioData(
                scenario_id=i,
                seed=seed,
                positions=positions,
                velocities=velocities,
                num_agents=num_agents,
                generated_time=datetime.datetime.now().isoformat(),
                metadata={
                    "generator": "randpose_unif_with_seed",
                    "parameters": {
                        "num_agents": num_agents,
                        "seed": seed
                    }
                }
            )
            scenarios.append(scenario)
        
        # 保存场景
        with open(scenario_file, 'wb') as f:
            pickle.dump(scenarios, f)
        
        print(f"场景已保存到: {scenario_file}")
        self.scenarios = scenarios
        return scenarios
    
    def load_scenarios(self, num_agents: int = 50) -> List[ScenarioData]:
        """
        加载场景
        
        Args:
            num_agents: 智能体数量
            
        Returns:
            加载的场景列表
        """
        scenario_file = self.get_scenario_file_path(num_agents)
        
        if not scenario_file.exists():
            print(f"场景文件不存在，将自动生成: {scenario_file}")
            return self.generate_scenarios(num_agents)
        
        with open(scenario_file, 'rb') as f:
            scenarios = pickle.load(f)
        
        print(f"已加载 {len(scenarios)} 个场景从: {scenario_file}")
        self.scenarios = scenarios
        return scenarios
    
    def get_train_scenario(self, num_agents: int = 50) -> ScenarioData:
        """
        获取训练场景（循环使用所有30个场景）
        
        Args:
            num_agents: 智能体数量
            
        Returns:
            训练场景数据
        """
        if not self.scenarios:
            self.load_scenarios(num_agents)
        
        scenario = self.scenarios[self.current_train_idx]
        self.current_train_idx = (self.current_train_idx + 1) % self.num_train_scenarios
        return scenario
    
    def get_eval_scenario(self, num_agents: int = 50) -> ScenarioData:
        """
        获取评估场景（循环使用前10个场景）
        
        Args:
            num_agents: 智能体数量
            
        Returns:
            评估场景数据
        """
        if not self.scenarios:
            self.load_scenarios(num_agents)
        
        scenario = self.scenarios[self.current_eval_idx]
        self.current_eval_idx = (self.current_eval_idx + 1) % self.num_eval_scenarios
        return scenario
    
    def reset_indices(self):
        """重置场景索引"""
        self.current_train_idx = 0
        self.current_eval_idx = 0
    
    def get_scenario_info(self, num_agents: int = 50) -> Dict:
        """获取场景信息"""
        if not self.scenarios:
            self.load_scenarios(num_agents)
        
        return {
            "total_scenarios": len(self.scenarios),
            "train_scenarios": self.num_train_scenarios,
            "eval_scenarios": self.num_eval_scenarios,
            "current_train_idx": self.current_train_idx,
            "current_eval_idx": self.current_eval_idx,
            "scenario_file": str(self.get_scenario_file_path(num_agents))
        }
    
    def validate_scenarios(self, num_agents: int = 50) -> bool:
        """
        验证场景的一致性
        
        Args:
            num_agents: 智能体数量
            
        Returns:
            验证是否通过
        """
        if not self.scenarios:
            self.load_scenarios(num_agents)
        
        print("验证场景一致性...")
        
        # 导入randpose_unif函数
        from test_seed_consistency import randpose_unif_with_seed
        
        for i, scenario in enumerate(self.scenarios[:5]):  # 验证前5个场景
            # 重新生成相同种子的场景
            new_pos, new_vel = randpose_unif_with_seed(num_agents, seed=scenario.seed)
            
            # 检查是否一致
            pos_match = np.allclose(scenario.positions, new_pos, atol=1e-10)
            vel_match = np.allclose(scenario.velocities, new_vel, atol=1e-10)
            
            if not (pos_match and vel_match):
                print(f"❌ 场景 {i} (种子 {scenario.seed}) 验证失败")
                return False
            else:
                print(f"✅ 场景 {i} (种子 {scenario.seed}) 验证通过")
        
        print("✅ 场景一致性验证通过")
        return True


if __name__ == "__main__":
    # 测试场景管理器
    manager = FixedScenarioManager()
    
    # 生成场景
    scenarios = manager.generate_scenarios(num_agents=50)
    
    # 验证场景
    manager.validate_scenarios(num_agents=50)
    
    # 测试获取场景
    print("\n测试场景获取:")
    for i in range(5):
        train_scenario = manager.get_train_scenario()
        eval_scenario = manager.get_eval_scenario()
        print(f"训练场景 {i}: 种子 {train_scenario.seed}")
        print(f"评估场景 {i}: 种子 {eval_scenario.seed}")
    
    # 显示场景信息
    print("\n场景信息:")
    info = manager.get_scenario_info()
    for k, v in info.items():
        print(f"  {k}: {v}")
