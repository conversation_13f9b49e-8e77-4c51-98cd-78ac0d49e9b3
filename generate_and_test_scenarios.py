"""
生成和测试固定场景的脚本
"""

import numpy as np
import matplotlib.pyplot as plt
from fixed_scenario_manager import FixedScenarioManager
import argparse


def generate_scenarios(num_agents=50, force_regenerate=False):
    """生成固定场景"""
    print("=" * 60)
    print("生成固定场景")
    print("=" * 60)
    
    manager = FixedScenarioManager()
    scenarios = manager.generate_scenarios(
        num_agents=num_agents, 
        force_regenerate=force_regenerate
    )
    
    print(f"成功生成 {len(scenarios)} 个场景")
    return manager


def test_scenario_consistency(manager, num_agents=50):
    """测试场景一致性"""
    print("\n" + "=" * 60)
    print("测试场景一致性")
    print("=" * 60)
    
    # 验证场景
    is_valid = manager.validate_scenarios(num_agents)
    
    if is_valid:
        print("✅ 所有场景验证通过")
    else:
        print("❌ 场景验证失败")
    
    return is_valid


def test_scenario_usage(manager, num_agents=50):
    """测试场景使用"""
    print("\n" + "=" * 60)
    print("测试场景使用")
    print("=" * 60)
    
    print("测试训练场景获取:")
    train_seeds = []
    for i in range(35):  # 测试超过30个，看是否循环
        scenario = manager.get_train_scenario(num_agents)
        train_seeds.append(scenario.seed)
        if i < 10 or i >= 30:  # 只打印前10个和循环后的5个
            print(f"  第{i+1}次: 种子 {scenario.seed}")
    
    print(f"\n前30个训练场景种子: {train_seeds[:30]}")
    print(f"循环后5个训练场景种子: {train_seeds[30:]}")
    print(f"是否正确循环: {train_seeds[:5] == train_seeds[30:]}")
    
    print("\n测试评估场景获取:")
    eval_seeds = []
    for i in range(15):  # 测试超过10个，看是否循环
        scenario = manager.get_eval_scenario(num_agents)
        eval_seeds.append(scenario.seed)
        if i < 10 or i >= 10:  # 只打印前10个和循环后的5个
            print(f"  第{i+1}次: 种子 {scenario.seed}")
    
    print(f"\n前10个评估场景种子: {eval_seeds[:10]}")
    print(f"循环后5个评估场景种子: {eval_seeds[10:]}")
    print(f"是否正确循环: {eval_seeds[:5] == eval_seeds[10:]}")
    
    # 验证评估场景是训练场景的子集
    train_first_10 = train_seeds[:10]
    eval_first_10 = eval_seeds[:10]
    print(f"\n评估场景是否为训练场景前10个: {train_first_10 == eval_first_10}")


def visualize_scenarios(manager, num_agents=50, num_to_show=6):
    """可视化场景"""
    print("\n" + "=" * 60)
    print("可视化场景")
    print("=" * 60)
    
    # 重置索引
    manager.reset_indices()
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(f'固定场景可视化 (前{num_to_show}个场景)', fontsize=16)
    
    for i in range(num_to_show):
        row = i // 3
        col = i % 3
        ax = axes[row, col]
        
        # 获取场景
        scenario = manager.get_train_scenario(num_agents)
        positions = scenario.positions
        
        # 绘制智能体位置
        ax.scatter(positions[0, :], positions[1, :], alpha=0.7, s=30)
        ax.set_title(f'场景 {scenario.scenario_id + 1} (种子 {scenario.seed})')
        ax.set_xlabel('X 坐标')
        ax.set_ylabel('Y 坐标')
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        # 设置坐标轴范围
        ax.set_xlim(-1, 3)
        ax.set_ylim(-1, 3)
    
    plt.tight_layout()
    plt.savefig('fixed_scenarios_visualization.png', dpi=300, bbox_inches='tight')
    print("场景可视化已保存到: fixed_scenarios_visualization.png")
    plt.show()


def analyze_scenario_diversity(manager, num_agents=50):
    """分析场景多样性"""
    print("\n" + "=" * 60)
    print("分析场景多样性")
    print("=" * 60)
    
    scenarios = manager.scenarios
    
    # 计算场景间的距离差异
    distances = []
    for i in range(len(scenarios)):
        for j in range(i+1, len(scenarios)):
            pos1 = scenarios[i].positions
            pos2 = scenarios[j].positions
            # 计算位置的平均欧氏距离
            dist = np.mean(np.linalg.norm(pos1 - pos2, axis=0))
            distances.append(dist)
    
    distances = np.array(distances)
    
    print(f"场景间平均距离: {np.mean(distances):.4f}")
    print(f"场景间距离标准差: {np.std(distances):.4f}")
    print(f"最小距离: {np.min(distances):.4f}")
    print(f"最大距离: {np.max(distances):.4f}")
    
    # 计算每个场景的分散程度
    spreads = []
    for scenario in scenarios:
        positions = scenario.positions
        center = np.mean(positions, axis=1)
        spread = np.mean(np.linalg.norm(positions - center.reshape(-1, 1), axis=0))
        spreads.append(spread)
    
    spreads = np.array(spreads)
    
    print(f"\n场景内分散程度:")
    print(f"平均分散程度: {np.mean(spreads):.4f}")
    print(f"分散程度标准差: {np.std(spreads):.4f}")
    print(f"最小分散程度: {np.min(spreads):.4f}")
    print(f"最大分散程度: {np.max(spreads):.4f}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成和测试固定场景')
    parser.add_argument('--num_agents', type=int, default=50, help='智能体数量')
    parser.add_argument('--force_regenerate', action='store_true', help='强制重新生成场景')
    parser.add_argument('--skip_visualization', action='store_true', help='跳过可视化')
    parser.add_argument('--skip_analysis', action='store_true', help='跳过多样性分析')
    
    args = parser.parse_args()
    
    # 生成场景
    manager = generate_scenarios(args.num_agents, args.force_regenerate)
    
    # 测试一致性
    is_valid = test_scenario_consistency(manager, args.num_agents)
    
    if not is_valid:
        print("❌ 场景验证失败，请检查实现")
        return
    
    # 测试使用
    test_scenario_usage(manager, args.num_agents)
    
    # 可视化场景
    if not args.skip_visualization:
        try:
            visualize_scenarios(manager, args.num_agents)
        except Exception as e:
            print(f"可视化失败: {e}")
    
    # 分析多样性
    if not args.skip_analysis:
        analyze_scenario_diversity(manager, args.num_agents)
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成")
    print("=" * 60)
    
    # 显示使用说明
    print("\n使用说明:")
    print("1. 场景文件已生成并保存")
    print("2. 在训练脚本中使用 CEEnvWithFixedScenarios")
    print("3. 设置 use_fixed_scenarios=True")
    print("4. 训练时 is_evaluation=False，评估时 is_evaluation=True")
    print("5. 训练将循环使用30个场景，评估将循环使用前10个场景")


if __name__ == "__main__":
    main()
