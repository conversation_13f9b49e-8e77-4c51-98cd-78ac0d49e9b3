"""
验证种子控制对randpose_unif函数的效果
测试相同种子是否能产生相同的初始位置
"""

import sys
import numpy as np
from pathlib import Path
from typing import Tuple

# 添加项目根目录到Python路径
current_file = Path(__file__)
project_root = current_file.parents[1]  # 回到auto_mst_rl_xuance目录
sys.path.insert(0, str(project_root))

from envs.sim_py.ce.tools.fixed_scenario_manager import randpose_unif_with_seed


def test_seed_consistency():
    """测试种子一致性"""
    print("=" * 60)
    print("测试种子控制对初始位置生成的影响")
    print("=" * 60)
    
    num_agents = 5
    test_seed = 42
    
    print(f"测试参数：智能体数量={num_agents}, 种子={test_seed}")
    print()
    
    # 测试1：相同种子多次调用
    print("测试1：相同种子多次调用")
    print("-" * 30)
    
    positions_1, _ = randpose_unif_with_seed(num_agents, seed=test_seed)
    positions_2, _ = randpose_unif_with_seed(num_agents, seed=test_seed)
    positions_3, _ = randpose_unif_with_seed(num_agents, seed=test_seed)
    
    # 检查是否完全相同
    is_same_1_2 = np.allclose(positions_1, positions_2, atol=1e-10)
    is_same_1_3 = np.allclose(positions_1, positions_3, atol=1e-10)
    is_same_2_3 = np.allclose(positions_2, positions_3, atol=1e-10)
    
    print(f"第1次与第2次结果相同: {is_same_1_2}")
    print(f"第1次与第3次结果相同: {is_same_1_3}")
    print(f"第2次与第3次结果相同: {is_same_2_3}")
    
    if is_same_1_2 and is_same_1_3 and is_same_2_3:
        print("✅ 相同种子产生完全相同的结果！")
    else:
        print("❌ 相同种子产生了不同的结果")
        
    # 显示具体位置（第一次的结果）
    print(f"\n种子{test_seed}生成的位置：")
    for i in range(num_agents):
        print(f"  智能体{i+1}: ({positions_1[0,i]:.6f}, {positions_1[1,i]:.6f})")
    
    print()
    
    # 测试2：不同种子的结果
    print("测试2：不同种子的结果")
    print("-" * 30)
    
    positions_seed_1, _ = randpose_unif_with_seed(num_agents, seed=1)
    positions_seed_2, _ = randpose_unif_with_seed(num_agents, seed=2)
    positions_seed_3, _ = randpose_unif_with_seed(num_agents, seed=100)
    
    # 检查是否不同
    is_different_1_2 = not np.allclose(positions_seed_1, positions_seed_2, atol=1e-6)
    is_different_1_3 = not np.allclose(positions_seed_1, positions_seed_3, atol=1e-6)
    is_different_2_3 = not np.allclose(positions_seed_2, positions_seed_3, atol=1e-6)
    
    print(f"种子1与种子2结果不同: {is_different_1_2}")
    print(f"种子1与种子100结果不同: {is_different_1_3}")
    print(f"种子2与种子100结果不同: {is_different_2_3}")
    
    if is_different_1_2 and is_different_1_3 and is_different_2_3:
        print("✅ 不同种子产生不同的结果！")
    else:
        print("❌ 不同种子产生了相同的结果")
    
    # 显示不同种子的位置差异
    print(f"\n不同种子生成的第1个智能体位置：")
    print(f"  种子1:   ({positions_seed_1[0,0]:.6f}, {positions_seed_1[1,0]:.6f})")
    print(f"  种子2:   ({positions_seed_2[0,0]:.6f}, {positions_seed_2[1,0]:.6f})")
    print(f"  种子100: ({positions_seed_3[0,0]:.6f}, {positions_seed_3[1,0]:.6f})")
    
    print()
    
    # 测试3：无种子的随机性
    print("测试3：无种子的随机性")
    print("-" * 30)
    
    positions_random_1, _ = randpose_unif_with_seed(num_agents, seed=None)
    positions_random_2, _ = randpose_unif_with_seed(num_agents, seed=None)
    
    is_random_different = not np.allclose(positions_random_1, positions_random_2, atol=1e-6)
    print(f"无种子时两次调用结果不同: {is_random_different}")
    
    if is_random_different:
        print("✅ 无种子时确实产生随机结果！")
    else:
        print("❌ 无种子时产生了相同结果（可能性极低）")
    
    print()
    print("=" * 60)
    print("结论：")
    print("1. 相同种子 → 完全相同的初始位置")
    print("2. 不同种子 → 不同的初始位置")
    print("3. 无种子   → 每次都随机")
    print("=" * 60)


if __name__ == "__main__":
    test_seed_consistency()
