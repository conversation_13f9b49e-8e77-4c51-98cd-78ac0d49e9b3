"""
支持固定场景的CE环境类
基于原有的ce_env.py，添加固定场景支持
"""

import numpy as np
from gym import spaces
from typing import Optional

from auto_mst_rl_xuance.envs.base.mst_env import MSTEnv
from auto_mst_rl_xuance.envs.sim_py.ce.algo.swarm_alg_ce import swarm_alg_ce
from auto_mst_rl_xuance.envs.sim_py.ce.tools.agent_utils import get_topology_neighbors, Agent
from auto_mst_rl_xuance.envs.sim_py.ce.tools.init_simulation import (
    create_initial_runtime_state,
    init_sim_robots,
)
from auto_mst_rl_xuance.envs.sim_py.ce.tools.simulation import parallel_sim_robots
from auto_mst_rl_xuance.envs.sim_py.ce.tools.fixed_scenario_manager import FixedScenarioManager


class CEEnvFixed(MSTEnv):
    """
    支持固定场景的CE环境类
    """

    def __init__(
        self,
        config: dict,
        max_episode_steps: int = 1200,
        enable_visualization: bool = False,
        use_fixed_scenarios: bool = True,
        num_train_scenarios: int = 30,
        num_eval_scenarios: int = 10,
        is_evaluation: bool = False,
        scenario_dir: str = None,
    ):
        """
        初始化CE环境

        参数:
            config (dict): 环境配置，包含仿真参数和奖励设置
            max_episode_steps (int): 最大步数
            enable_visualization (bool): 是否启用实时可视化
            use_fixed_scenarios (bool): 是否使用固定场景
            num_train_scenarios (int): 训练场景数量
            num_eval_scenarios (int): 评估场景数量
            is_evaluation (bool): 是否为评估模式
            scenario_dir (str): 场景文件目录
        """
        # 初始化基类
        super(CEEnvFixed, self).__init__(config, max_episode_steps)
        
        # 设置场景类型编码
        self.scenario_type_code = 0  # Chase-Escape场景

        # 实时可视化相关设置
        self.enable_visualization = enable_visualization
        self.visualization_fig = None
        self.visualization_axes = None

        # 固定场景相关设置
        self.use_fixed_scenarios = use_fixed_scenarios
        self.is_evaluation = is_evaluation
        
        if self.use_fixed_scenarios:
            self.scenario_manager = FixedScenarioManager(
                scenario_dir=scenario_dir,
                num_train_scenarios=num_train_scenarios,
                num_eval_scenarios=num_eval_scenarios
            )
            
            # 预加载场景
            max_id = getattr(self.config, "max_id", 50)
            self.scenario_manager.load_scenarios(max_id)
            
            print(f"固定场景模式: {'评估' if is_evaluation else '训练'}")

        # 初始化仿真环境
        self._reset_simulation()

        # 确保agents列表在初始化时正确设置
        if not hasattr(self, "agents") or not self.agents:
            self.agents = [str(i) for i in self.runtime_state.robots_list]

        # 确保n_agents属性正确设置
        if not hasattr(self, "n_agents") or self.n_agents == 0:
            self.n_agents = len(self.agents)

        # 构建观察和动作空间
        for agent_id in self.agents:
            self.observation_space[agent_id] = spaces.Box(
                low=-10.0, high=10.0, shape=(self._get_obs_dim(),)
            )
            self.action_space[agent_id] = spaces.Box(
                low=self.mst_min, high=self.mst_max, shape=(1,)
            )

    def _reset_simulation(self):
        """
        重置CE仿真环境
        """
        # 获取配置参数
        max_id = getattr(self.config, "max_id", 50)

        # 初始化运行时状态
        self.runtime_state = create_initial_runtime_state(
            max_id=max_id, max_sim_steps_val=self.max_episode_steps
        )

        if self.use_fixed_scenarios:
            # 使用固定场景
            self._init_with_fixed_scenario(max_id)
        else:
            # 使用原有的随机初始化
            self._init_with_random_scenario(max_id)

    def _init_with_fixed_scenario(self, max_id: int):
        """
        使用固定场景初始化
        
        Args:
            max_id: 智能体数量
        """
        # 获取场景数据
        if self.is_evaluation:
            scenario_data = self.scenario_manager.get_eval_scenario(max_id)
        else:
            scenario_data = self.scenario_manager.get_train_scenario(max_id)
        
        # 使用场景数据初始化智能体
        self._init_robots_from_scenario(scenario_data)

    def _init_with_random_scenario(self, max_id: int):
        """
        使用随机场景初始化（原有方式）
        
        Args:
            max_id: 智能体数量
        """
        self.runtime_state = init_sim_robots(
            self.runtime_state,
            "unif",  # 均匀分布初始化
            np.array([0, 0]),  # 中心位置
            100,  # 分布半径
            True,  # 并行仿真
        )

    def _init_robots_from_scenario(self, scenario_data):
        """
        从场景数据初始化智能体
        
        Args:
            scenario_data: 场景数据
        """
        # 初始化机器人列表
        self.runtime_state.robots_list = list(range(1, self.runtime_state.max_id + 1))
        
        # 创建智能体对象
        self.runtime_state.actors = {}
        
        for i, robot_id in enumerate(self.runtime_state.robots_list):
            # 从场景数据获取位置和速度
            pos = scenario_data.positions[:, i]
            vel = scenario_data.velocities[:, i]
            
            # 创建智能体
            agent = Agent(
                id=robot_id,
                pose=pos.copy(),
                vel=vel.copy(),
                is_activated=False,
                src_id=None,
                cj_threshold=self.runtime_state.config.cj_threshold,
                memory=[],
                desired_turn_angle=[],
                desired_speed=[],
                observed_neighbor_ids=[],
                observed_neighbor_cj_values=[],
                observed_neighbor_relative_positions=[]
            )
            
            self.runtime_state.actors[robot_id] = agent

        # 添加捕食者（hawk）
        hawk_id = self.runtime_state.config.hawk_id
        if hawk_id not in self.runtime_state.actors:
            # 捕食者位置可以固定或基于场景调整
            hawk_pos = np.array([0.0, 0.0])  # 可以根据需要调整
            hawk_vel = np.array([0.0, 0.0])
            
            hawk_agent = Agent(
                id=hawk_id,
                pose=hawk_pos,
                vel=hawk_vel,
                is_activated=False,
                src_id=None,
                cj_threshold=float('inf'),  # 捕食者不会被激活
                memory=[],
                desired_turn_angle=[],
                desired_speed=[],
                observed_neighbor_ids=[],
                observed_neighbor_cj_values=[],
                observed_neighbor_relative_positions=[]
            )
            
            self.runtime_state.actors[hawk_id] = hawk_agent

    def _simulation_step(self):
        """
        执行CE仿真步进
        """
        # 更新仿真步骤计数器
        self.runtime_state.sim_step = self.episode_step + 1

        # 计算期望转向角度和速度
        des_turn_angle, des_speed, self.runtime_state = swarm_alg_ce(self.runtime_state)

        # 更新智能体状态
        self.runtime_state = parallel_sim_robots(
            self.runtime_state, des_turn_angle, des_speed
        )

    def get_scenario_info(self) -> dict:
        """
        获取当前场景信息
        
        Returns:
            场景信息字典
        """
        if self.use_fixed_scenarios:
            max_id = getattr(self.config, "max_id", 50)
            return self.scenario_manager.get_scenario_info(max_id)
        else:
            return {"mode": "random", "use_fixed_scenarios": False}

    def reset_scenario_indices(self):
        """重置场景索引（用于新的训练/评估周期）"""
        if self.use_fixed_scenarios:
            self.scenario_manager.reset_indices()

    # 以下方法继承自原有的CEEnv实现
    def _get_agent_obs(self, agent_id):
        """获取CE场景中单个智能体的观察"""
        actual_id = int(agent_id)
        agent = self.runtime_state.actors[actual_id]

        # 获取邻居
        neighbors, num_neighbors, neighbors_id_list = get_topology_neighbors(
            self.runtime_state, actual_id
        )

        # A. 自身状态 (4维)
        vel_magnitude = np.linalg.norm(agent.vel)
        if vel_magnitude > 0:
            norm_vx, norm_vy = agent.vel / vel_magnitude
        else:
            norm_vx, norm_vy = 0.0, 0.0

        self_state = np.array([
            norm_vx,
            norm_vy,
            vel_magnitude / self.runtime_state.config.v0,
            1.0 if agent.is_activated else 0.0,
        ])

        # B. 邻居统计信息 (3维)
        if len(neighbors_id_list) > 0:
            activated_neighbors = sum(1 for nid in neighbors_id_list 
                                    if self.runtime_state.actors[nid].is_activated)
            neighbor_stats = np.array([
                len(neighbors_id_list) / self.k_neighbors,
                activated_neighbors / len(neighbors_id_list),
                0.0  # 预留维度
            ])
        else:
            neighbor_stats = np.zeros(3)

        # C. 邻居详细信息 (5*k_neighbors维)
        neighbor_details = np.zeros(5 * self.k_neighbors)
        for i in range(min(len(neighbors_id_list), self.k_neighbors)):
            neighbor_id = neighbors_id_list[i]
            neighbor = self.runtime_state.actors[neighbor_id]
            
            # 相对位置
            rel_pos = neighbor.pose - agent.pose
            rel_dist = np.linalg.norm(rel_pos)
            if rel_dist > 0:
                norm_rel_pos = rel_pos / rel_dist
            else:
                norm_rel_pos = np.array([0.0, 0.0])
            
            # 相对速度
            rel_vel = neighbor.vel - agent.vel
            rel_vel_mag = np.linalg.norm(rel_vel)
            
            neighbor_details[i*5:(i+1)*5] = [
                norm_rel_pos[0],
                norm_rel_pos[1], 
                min(1.0, rel_dist / 1000.0),
                min(1.0, rel_vel_mag / 50.0),
                1.0 if neighbor.is_activated else 0.0
            ]

        # D. 场景与任务信息 (5维)
        scenario_info = np.zeros(5)
        scenario_info[0] = self.scenario_type_code

        # CE场景特定信息：捕食者相关
        hawk_id = self.runtime_state.config.hawk_id
        if hawk_id in self.runtime_state.actors:
            hawk = self.runtime_state.actors[hawk_id]
            vec_to_hawk = hawk.pose - agent.pose
            dist_to_hawk = np.linalg.norm(vec_to_hawk)
            
            if dist_to_hawk > 0:
                norm_dir_to_hawk = vec_to_hawk / dist_to_hawk
                scenario_info[1] = norm_dir_to_hawk[0]
                scenario_info[2] = norm_dir_to_hawk[1]
                scenario_info[3] = min(1.0, dist_to_hawk / 2000.0)

        # 组合所有特征
        observation = np.concatenate([
            self_state, neighbor_stats, neighbor_details, scenario_info
        ]).astype(np.float32)

        # 处理异常值
        observation = np.nan_to_num(observation, nan=0.0, posinf=1.0, neginf=-1.0)
        observation = np.clip(observation, -10.0, 10.0)

        return observation
    
    def _compute_rewards(self):
        """计算CE场景的奖励"""
        global_reward = self._compute_global_reward()
        return {agent_id: global_reward for agent_id in self.agents}
    
    def _compute_global_reward(self):
        """计算CE场景的全局奖励"""
        # 简化的奖励函数，可以根据需要扩展
        TIME_STEP_SURVIVAL_REWARD = 0.1
        return TIME_STEP_SURVIVAL_REWARD
    
    def _get_termination(self):
        """检查终止条件"""
        # 简化的终止条件，可以根据需要扩展
        return False
    
    def state(self):
        """返回全局状态"""
        # 简化的全局状态，可以根据需要扩展
        state = np.zeros(20)
        state[0] = self.scenario_type_code
        state[1] = self.episode_step / self.max_episode_steps
        return state
