"""
固定场景管理器
用于生成、保存和加载基于固定种子的CE场景
"""

import os
import pickle
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import datetime


@dataclass
class ScenarioData:
    """单个场景的数据结构"""
    scenario_id: int
    seed: int
    positions: np.ndarray  # (2, num_agents) 位置矩阵
    velocities: np.ndarray  # (2, num_agents) 速度矩阵
    num_agents: int
    generated_time: str
    metadata: Dict


def randpose_unif_with_seed(num_agents: int, seed: int = None) -> Tuple[np.ndarray, np.ndarray]:
    """
    生成均匀分布的随机位置和速度（带种子控制）
    这是utils.py中randpose_unif函数的带种子版本
    """
    if seed is not None:
        np.random.seed(seed)
    
    t_max = 10  # 迭代最大步数
    dt = 0.1  # 时间步长(s)
    la = 3  # 排斥相关因子
    lc = 10  # 吸引相关因子
    max_force = 5  # 最大加速度
    v_0 = 0  # 自驱动速度大小
    relax_time = 0.1  # 自驱动相关因子
    noise_str = 0.5  # 噪声强度

    # 初始化智能体位置和速度
    pos = np.random.rand(2, num_agents) * 2  # 初始位置均匀分布
    vel = np.ones((2, num_agents))  # 初始速度~0，基本静止

    for t in range(t_max):
        # 自驱动力
        u_auto = np.zeros((2, num_agents))
        i_rate = np.sqrt(vel[0, :] ** 2 + vel[1, :] ** 2)
        i_rate[i_rate == 0] = 1e-10  # 避免除零
        u_auto = np.tile((v_0 - i_rate) / relax_time, (2, 1)) * (
            vel / np.tile(i_rate, (2, 1))
        )

        # 吸引/排斥力
        u_grad = np.zeros((2, num_agents))
        for i in range(num_agents):
            rij = pos - np.tile(pos[:, i].reshape(2, 1), (1, num_agents))
            dij = np.sqrt(rij[0, :] ** 2 + rij[1, :] ** 2)
            dij[dij == 0] = 1e-10  # 避免除零
            nij = rij / np.tile(dij, (2, 1))
            ra = (1 - (la / dij) ** 2) * np.exp(-dij / lc)
            temp = np.tile(ra, (2, 1)) * nij
            u_grad[:, i] = np.nansum(temp, axis=1)

        # 状态更新
        u_sum = u_auto + u_grad
        force = np.sqrt(u_sum[0, :] ** 2 + u_sum[1, :] ** 2)
        force[force == 0] = 1e-10  # 避免除零
        ut = np.tile(np.minimum(max_force, force), (2, 1)) * (
            u_sum / np.tile(force, (2, 1))
        )
        u = ut + np.random.uniform(-noise_str, noise_str, (2, num_agents))
        vel = vel + u * dt
        pos = pos + vel * dt

    return pos, vel


class FixedScenarioManager:
    """固定场景管理器"""
    
    def __init__(self, 
                 scenario_dir: str = None,
                 num_train_scenarios: int = 30,
                 num_eval_scenarios: int = 10):
        """
        初始化场景管理器
        
        Args:
            scenario_dir: 场景文件存储目录，默认为项目根目录下的scenarios
            num_train_scenarios: 训练场景数量
            num_eval_scenarios: 评估场景数量
        """
        if scenario_dir is None:
            # 默认使用项目根目录下的scenarios文件夹
            current_file = Path(__file__)
            project_root = current_file.parents[5]  # 回到项目根目录
            self.scenario_dir = project_root / "scenarios"
        else:
            self.scenario_dir = Path(scenario_dir)
            
        self.scenario_dir.mkdir(exist_ok=True)
        
        self.num_train_scenarios = num_train_scenarios
        self.num_eval_scenarios = num_eval_scenarios
        
        # 确保评估场景数量不超过训练场景数量
        assert num_eval_scenarios <= num_train_scenarios, \
            f"评估场景数量({num_eval_scenarios})不能超过训练场景数量({num_train_scenarios})"
        
        self.scenarios: List[ScenarioData] = []
        self.current_train_idx = 0
        self.current_eval_idx = 0
    
    def get_scenario_file_path(self, num_agents: int) -> Path:
        """获取场景文件路径"""
        return self.scenario_dir / f"ce_fixed_scenarios_{self.num_train_scenarios}_{num_agents}.pkl"
    
    def generate_scenarios(self, num_agents: int = 50, force_regenerate: bool = False) -> List[ScenarioData]:
        """
        生成固定场景
        
        Args:
            num_agents: 智能体数量
            force_regenerate: 是否强制重新生成
            
        Returns:
            生成的场景列表
        """
        scenario_file = self.get_scenario_file_path(num_agents)
        
        # 如果文件存在且不强制重新生成，直接加载
        if scenario_file.exists() and not force_regenerate:
            print(f"场景文件已存在: {scenario_file}")
            return self.load_scenarios(num_agents)
        
        print(f"开始生成 {self.num_train_scenarios} 个固定场景...")
        
        scenarios = []
        for i in range(self.num_train_scenarios):
            seed = i + 1  # 种子从1开始
            print(f"生成场景 {i+1}/{self.num_train_scenarios} (种子: {seed})")
            
            # 使用固定种子生成位置
            positions, velocities = randpose_unif_with_seed(num_agents, seed=seed)
            
            # 创建场景数据
            scenario = ScenarioData(
                scenario_id=i,
                seed=seed,
                positions=positions,
                velocities=velocities,
                num_agents=num_agents,
                generated_time=datetime.datetime.now().isoformat(),
                metadata={
                    "generator": "randpose_unif_with_seed",
                    "parameters": {
                        "num_agents": num_agents,
                        "seed": seed
                    }
                }
            )
            scenarios.append(scenario)
        
        # 保存场景
        with open(scenario_file, 'wb') as f:
            pickle.dump(scenarios, f)
        
        print(f"场景已保存到: {scenario_file}")
        self.scenarios = scenarios
        return scenarios
    
    def load_scenarios(self, num_agents: int = 50) -> List[ScenarioData]:
        """
        加载场景
        
        Args:
            num_agents: 智能体数量
            
        Returns:
            加载的场景列表
        """
        scenario_file = self.get_scenario_file_path(num_agents)
        
        if not scenario_file.exists():
            print(f"场景文件不存在，将自动生成: {scenario_file}")
            return self.generate_scenarios(num_agents)
        
        with open(scenario_file, 'rb') as f:
            scenarios = pickle.load(f)
        
        print(f"已加载 {len(scenarios)} 个场景从: {scenario_file}")
        self.scenarios = scenarios
        return scenarios
    
    def get_train_scenario(self, num_agents: int = 50) -> ScenarioData:
        """
        获取训练场景（循环使用所有30个场景）
        
        Args:
            num_agents: 智能体数量
            
        Returns:
            训练场景数据
        """
        if not self.scenarios:
            self.load_scenarios(num_agents)
        
        scenario = self.scenarios[self.current_train_idx]
        self.current_train_idx = (self.current_train_idx + 1) % self.num_train_scenarios
        return scenario
    
    def get_eval_scenario(self, num_agents: int = 50) -> ScenarioData:
        """
        获取评估场景（循环使用前10个场景）
        
        Args:
            num_agents: 智能体数量
            
        Returns:
            评估场景数据
        """
        if not self.scenarios:
            self.load_scenarios(num_agents)
        
        scenario = self.scenarios[self.current_eval_idx]
        self.current_eval_idx = (self.current_eval_idx + 1) % self.num_eval_scenarios
        return scenario
    
    def reset_indices(self):
        """重置场景索引"""
        self.current_train_idx = 0
        self.current_eval_idx = 0
    
    def get_scenario_info(self, num_agents: int = 50) -> Dict:
        """获取场景信息"""
        if not self.scenarios:
            self.load_scenarios(num_agents)
        
        return {
            "total_scenarios": len(self.scenarios),
            "train_scenarios": self.num_train_scenarios,
            "eval_scenarios": self.num_eval_scenarios,
            "current_train_idx": self.current_train_idx,
            "current_eval_idx": self.current_eval_idx,
            "scenario_file": str(self.get_scenario_file_path(num_agents))
        }
