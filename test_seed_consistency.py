"""
验证种子控制对randpose_unif函数的效果
测试相同种子是否能产生相同的初始位置
"""

import numpy as np
from typing import <PERSON><PERSON>


def randpose_unif_with_seed(num_agents: int, seed: int = None) -> Tuple[np.ndarray, np.ndarray]:
    """
    生成均匀分布的随机位置和速度（带种子控制）
    
    Args:
        num_agents: 智能体数量
        seed: 随机种子
        
    Returns:
        tuple: (位置矩阵(2×N), 速度矩阵(2×N))
    """
    # 设置种子
    if seed is not None:
        np.random.seed(seed)
    
    t_max = 10  # 迭代最大步数
    dt = 0.1  # 时间步长(s)
    la = 3  # 排斥相关因子
    lc = 10  # 吸引相关因子
    max_force = 5  # 最大加速度
    v_0 = 0  # 自驱动速度大小
    relax_time = 0.1  # 自驱动相关因子
    noise_str = 0.5  # 噪声强度

    # 初始化智能体位置和速度
    pos = np.random.rand(2, num_agents) * 2  # 初始位置均匀分布
    vel = np.ones((2, num_agents))  # 初始速度~0，基本静止

    for t in range(t_max):
        # 自驱动力
        u_auto = np.zeros((2, num_agents))
        i_rate = np.sqrt(vel[0, :] ** 2 + vel[1, :] ** 2)
        i_rate[i_rate == 0] = 1e-10  # 避免除零
        u_auto = np.tile((v_0 - i_rate) / relax_time, (2, 1)) * (
            vel / np.tile(i_rate, (2, 1))
        )

        # 吸引/排斥力
        u_grad = np.zeros((2, num_agents))
        for i in range(num_agents):
            rij = pos - np.tile(pos[:, i].reshape(2, 1), (1, num_agents))
            dij = np.sqrt(rij[0, :] ** 2 + rij[1, :] ** 2)
            dij[dij == 0] = 1e-10  # 避免除零
            nij = rij / np.tile(dij, (2, 1))
            ra = (1 - (la / dij) ** 2) * np.exp(-dij / lc)
            temp = np.tile(ra, (2, 1)) * nij
            u_grad[:, i] = np.nansum(temp, axis=1)

        # 状态更新
        u_sum = u_auto + u_grad
        force = np.sqrt(u_sum[0, :] ** 2 + u_sum[1, :] ** 2)
        force[force == 0] = 1e-10  # 避免除零
        ut = np.tile(np.minimum(max_force, force), (2, 1)) * (
            u_sum / np.tile(force, (2, 1))
        )
        u = ut + np.random.uniform(-noise_str, noise_str, (2, num_agents))
        vel = vel + u * dt
        pos = pos + vel * dt

    return pos, vel


def test_seed_consistency():
    """测试种子一致性"""
    print("=" * 60)
    print("测试种子控制对初始位置生成的影响")
    print("=" * 60)
    
    num_agents = 5
    test_seed = 42
    
    print(f"测试参数：智能体数量={num_agents}, 种子={test_seed}")
    print()
    
    # 测试1：相同种子多次调用
    print("测试1：相同种子多次调用")
    print("-" * 30)
    
    positions_1, _ = randpose_unif_with_seed(num_agents, seed=test_seed)
    positions_2, _ = randpose_unif_with_seed(num_agents, seed=test_seed)
    positions_3, _ = randpose_unif_with_seed(num_agents, seed=test_seed)
    
    # 检查是否完全相同
    is_same_1_2 = np.allclose(positions_1, positions_2, atol=1e-10)
    is_same_1_3 = np.allclose(positions_1, positions_3, atol=1e-10)
    is_same_2_3 = np.allclose(positions_2, positions_3, atol=1e-10)
    
    print(f"第1次与第2次结果相同: {is_same_1_2}")
    print(f"第1次与第3次结果相同: {is_same_1_3}")
    print(f"第2次与第3次结果相同: {is_same_2_3}")
    
    if is_same_1_2 and is_same_1_3 and is_same_2_3:
        print("✅ 相同种子产生完全相同的结果！")
    else:
        print("❌ 相同种子产生了不同的结果")
        
    # 显示具体位置（第一次的结果）
    print(f"\n种子{test_seed}生成的位置：")
    for i in range(num_agents):
        print(f"  智能体{i+1}: ({positions_1[0,i]:.6f}, {positions_1[1,i]:.6f})")
    
    print()
    
    # 测试2：不同种子的结果
    print("测试2：不同种子的结果")
    print("-" * 30)
    
    positions_seed_1, _ = randpose_unif_with_seed(num_agents, seed=1)
    positions_seed_2, _ = randpose_unif_with_seed(num_agents, seed=2)
    positions_seed_3, _ = randpose_unif_with_seed(num_agents, seed=100)
    
    # 检查是否不同
    is_different_1_2 = not np.allclose(positions_seed_1, positions_seed_2, atol=1e-6)
    is_different_1_3 = not np.allclose(positions_seed_1, positions_seed_3, atol=1e-6)
    is_different_2_3 = not np.allclose(positions_seed_2, positions_seed_3, atol=1e-6)
    
    print(f"种子1与种子2结果不同: {is_different_1_2}")
    print(f"种子1与种子100结果不同: {is_different_1_3}")
    print(f"种子2与种子100结果不同: {is_different_2_3}")
    
    if is_different_1_2 and is_different_1_3 and is_different_2_3:
        print("✅ 不同种子产生不同的结果！")
    else:
        print("❌ 不同种子产生了相同的结果")
    
    # 显示不同种子的位置差异
    print(f"\n不同种子生成的第1个智能体位置：")
    print(f"  种子1:   ({positions_seed_1[0,0]:.6f}, {positions_seed_1[1,0]:.6f})")
    print(f"  种子2:   ({positions_seed_2[0,0]:.6f}, {positions_seed_2[1,0]:.6f})")
    print(f"  种子100: ({positions_seed_3[0,0]:.6f}, {positions_seed_3[1,0]:.6f})")
    
    print()
    
    # 测试3：无种子的随机性
    print("测试3：无种子的随机性")
    print("-" * 30)
    
    positions_random_1, _ = randpose_unif_with_seed(num_agents, seed=None)
    positions_random_2, _ = randpose_unif_with_seed(num_agents, seed=None)
    
    is_random_different = not np.allclose(positions_random_1, positions_random_2, atol=1e-6)
    print(f"无种子时两次调用结果不同: {is_random_different}")
    
    if is_random_different:
        print("✅ 无种子时确实产生随机结果！")
    else:
        print("❌ 无种子时产生了相同结果（可能性极低）")
    
    print()
    print("=" * 60)
    print("结论：")
    print("1. 相同种子 → 完全相同的初始位置")
    print("2. 不同种子 → 不同的初始位置")
    print("3. 无种子   → 每次都随机")
    print("=" * 60)


if __name__ == "__main__":
    test_seed_consistency()
