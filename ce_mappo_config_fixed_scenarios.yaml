# Chase-Escape场景的MAPPO算法配置 - 固定场景版本
# 基础设置
dl_toolbox: "torch"  # 深度学习框架: "torch", "mindspore", "tensorlayer"
project_name: "mst_fixed_scenarios"
logger: "tensorboard"  # 日志工具: "tensorboard", "wandb"
wandb_user_name: "auto_mst_rl"
test_mode: false
device: "cuda:0"  # 计算设备: "cpu", "cuda:0"
distributed_training: false  # 是否使用分布式训练
master_port: "12355"  # 分布式训练主端口
clip_type: 1  # 梯度裁剪类型

# 环境设置
agent: "MAPPO"
env_name: "CEEnvWithFixedScenarios"  # 使用新的环境类
env_id: "CEEnvWithFixedScenarios"  # 环境ID
env_seed: 42  # 环境随机种子（用于其他随机性，如噪声）
continuous_action: true  # 连续动作空间
render: false  # 是否开启渲染
render_mode: null
fps: 30  # 渲染帧率
vectorize: "SubprocVecMultiAgentEnv"  # 向量化环境类型
running_steps: 2000000  # 总运行步数
eval_interval: 5000  # 评估间隔
test_episode: 10  # 测试轮数, 对应10个固定的测试场景

# 固定场景设置
use_fixed_scenarios: true  # 是否使用固定场景
num_train_scenarios: 30   # 训练场景数量
num_eval_scenarios: 10    # 评估场景数量（使用前10个训练场景）
scenario_dir: "scenarios" # 场景文件存储目录
force_regenerate_scenarios: false  # 是否强制重新生成场景

# 环境特定参数
max_id: 50  # 智能体数量
max_episode_steps: 1200  # 最大步数
mst_min: 0.0  # MST阈值的最小值
mst_max: 20  # MST阈值的最大值
k_neighbors: 7  # 观察中考虑的邻居数量
normalize_obs: true  # 归一化观察
normalize_state: true  # 归一化状态

# 算法设置
algo_name: "MAPPO"
learner: "MAPPO_Clip_Learner"
policy: "Gaussian_MAAC_Policy"  # 连续动作的高斯策略
representation: "Basic_MLP"

# 网络架构设置
representation_args:  # 表示网络参数
  hidden_sizes: [256, 128]
  activation: "relu"
representation_hidden_size: [256, 128]  # 表示网络隐藏层大小
actor_hidden_size: [128, 128]  # Actor网络隐藏层大小
critic_hidden_size: [128, 128]  # Critic网络隐藏层大小
activation: "relu"  # 激活函数
activation_action: "tanh"  # 动作输出层的激活函数
use_parameter_sharing: true  # 是否使用参数共享
use_actions_mask: false  # 是否使用动作掩码

# RNN设置（当使用RNN时）
use_rnn: false  # 是否使用循环神经网络
rnn: "GRU"  # 循环层类型: "GRU", "LSTM"
N_recurrent_layers: 1  # 循环层数量
recurrent_hidden_size: 64  # 循环层隐藏单元数量
dropout: 0  # 丢弃率
normalize: "LayerNorm"  # 层归一化
initialize: "orthogonal"  # 网络初始化方式
gain: 0.01  # 初始化增益

# 训练参数
seed: 1  # 随机种子（用于神经网络初始化等）
parallels: 16  # 并行环境数量
buffer_size: 4096  # 每个并行环境的缓冲区大小
n_epochs: 10  # 每批数据的训练次数
n_minibatch: 2  # 小批量数量
learning_rate: 3.0e-4  # 学习率
lr_a: 3.0e-4  # Actor学习率
lr_c: 3.0e-4  # Critic学习率
weight_decay: 0  # 权重衰减
lr_clip_range: [1.0e-6, 1.0e-6]  # 学习率裁剪范围

# PPO参数
vf_coef: 0.5  # 价值函数系数
ent_coef: 0.01  # 熵系数
target_kl: 0.015  # KL散度目标
clip_range: 0.2  # PPO裁剪范围
gamma: 0.99  # 折扣因子
gae_lambda: 0.95  # GAE参数
use_gae: true  # 是否使用GAE
use_value_clip: true  # 是否使用价值裁剪
value_clip_range: 0.2  # 价值裁剪范围
use_value_norm: true  # 是否使用价值归一化
use_huber_loss: true  # 是否使用Huber损失
huber_delta: 10.0  # Huber损失参数

# 其他设置
use_global_state: true  # 是否使用全局状态（用于中心化Critic）
use_state_norm: false  # 是否使用状态归一化
use_reward_norm: false  # 是否使用奖励归一化
use_popart: false  # 是否使用PopArt
use_valuenorm: false  # 是否使用价值归一化
use_feature_normalization: true  # 是否使用特征归一化
use_orthogonal_init: true  # 是否使用正交初始化

# 日志和保存设置
log_dir: "./logs"  # 日志目录
model_dir: "./models"  # 模型保存目录
save_interval: 50000  # 模型保存间隔
log_interval: 1000  # 日志记录间隔

# 评估设置
eval_episodes: 10  # 每次评估的episode数量
eval_interval: 5000  # 评估间隔
save_replay: false  # 是否保存回放
replay_dir: "./replays"  # 回放保存目录

# 调试设置
debug: false  # 是否开启调试模式
verbose: 1  # 详细程度
print_interval: 1000  # 打印间隔
